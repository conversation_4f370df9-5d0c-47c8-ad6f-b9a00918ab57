import { Migration } from '@mikro-orm/migrations';

export class Migration20250715120000 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table "user_embedded_wallet" add column "display_order" integer null;`);
    this.addSql(`alter table "user_managed_wallet" add column "display_order" integer null;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table "user_embedded_wallet" drop column "display_order";`);
    this.addSql(`alter table "user_managed_wallet" drop column "display_order";`);
  }

}
