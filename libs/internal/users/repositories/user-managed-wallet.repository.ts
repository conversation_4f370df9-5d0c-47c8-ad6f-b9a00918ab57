import { EntityManager, EntityRepository } from '@mikro-orm/core';
import { User } from '../entities/user.entity';
import { UserManagedWallet } from '../entities/user-managed-wallet.entity';
import { ManagedWalletDto } from '../dto/managed-wallet.dto';
import { ApiError } from 'libs/common/api-errors';
import { WALLET_NOT_FOUND, WALLET_ACCESS_DENIED } from 'libs/common/api-errors/errors';

export class UserManagedWalletRepository extends EntityRepository<UserManagedWallet> {
    async createWalletsForUser(
        em: EntityManager,
        user: User,
        wallets: ManagedWalletDto[],
    ): Promise<UserManagedWallet[]> {
        // const em = this.em.fork();
        const result: UserManagedWallet[] = [];
        for (const wallet of wallets) {
            const w = new UserManagedWallet();
            w.user = user;
            w.chain = wallet.chain;
            w.walletAddress = wallet.walletAddress;
            w.encryptedPrivateKey = wallet.encryptedPrivateKey;
            await em.persistAndFlush(w);
            result.push(w);
        }
        return result;
    }

    async updateWalletDisplayOrder(
        em: EntityManager,
        user: User,
        walletId: string,
        displayOrder: number,
    ): Promise<UserManagedWallet> {
        const wallet = await em.findOne(UserManagedWallet, { id: walletId, user: user.id });

        if (!wallet) {
            throw new ApiError(WALLET_NOT_FOUND);
        }

        // Additional security check to ensure the wallet belongs to the user
        if (wallet.user.id !== user.id) {
            throw new ApiError(WALLET_ACCESS_DENIED);
        }

        wallet.displayOrder = displayOrder;
        await em.persistAndFlush(wallet);

        return wallet;
    }

    async updateMultipleWalletDisplayOrders(
        em: EntityManager,
        user: User,
        walletOrders: Array<{ id: string; displayOrder: number }>,
    ): Promise<UserManagedWallet[]> {
        const walletIds = walletOrders.map((wo) => wo.id);
        const wallets = await em.find(UserManagedWallet, {
            id: { $in: walletIds },
            user: user.id,
        });

        if (wallets.length !== walletIds.length) {
            throw new ApiError(WALLET_NOT_FOUND);
        }

        // Update display orders
        const updatedWallets: UserManagedWallet[] = [];
        for (const walletOrder of walletOrders) {
            const wallet = wallets.find((w) => w.id === walletOrder.id);
            if (wallet) {
                wallet.displayOrder = walletOrder.displayOrder;
                updatedWallets.push(wallet);
            }
        }

        await em.persistAndFlush(updatedWallets);
        return updatedWallets;
    }

    async getUserWalletsOrderedByDisplayOrder(
        em: EntityManager,
        user: User,
    ): Promise<UserManagedWallet[]> {
        return await em.find(
            UserManagedWallet,
            { user: user.id },
            {
                orderBy: [
                    { displayOrder: 'ASC' },
                    { createdAt: 'ASC' },
                ],
            },
        );
    }
}
