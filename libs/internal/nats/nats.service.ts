import { Inject, Injectable, OnModuleInit } from '@nestjs/common';
import { appConfig } from 'libs/configs';
import { JetStreamClient, JetStreamManager, NatsConnection, RetentionPolicy, StorageType } from 'nats';
import { InjectPinoLogger } from 'nestjs-pino';

@Injectable()
export class NatsService implements OnModuleInit {
    private js: JetStreamClient;
    constructor(
        @Inject('NATS_CLIENT') private readonly natsClient: NatsConnection,
        @InjectPinoLogger(NatsService.name) private readonly logger: any,
    ) {
        this.js = natsClient.jetstream();
    }
    async onModuleInit() {
        const jsm: JetStreamManager = await this.natsClient.jetstreamManager();

        // Check if the stream exists
        try {
            await jsm.streams.info(appConfig.NATS_USER_STREAM);
            this.logger.info(`✅ Stream ${appConfig.NATS_USER_STREAM} already exists`);
        } catch (err) {
            this.logger.info(`🛠️ Stream ${appConfig.NATS_USER_STREAM} not found, creating...`);
            await jsm.streams.add({
                name: appConfig.NATS_USER_STREAM,
                subjects: ['user.>'],
                retention: RetentionPolicy.Limits,
                storage: StorageType.File,
            });
            this.logger.info(`✅ Stream ${appConfig.NATS_USER_STREAM} created`);
        }
    }

    async publish<T>(subject: string, data: T) {
        // add try-catch to prevent breaking main business logic
        try {
            await this.js.publish(subject, JSON.stringify(data));
        } catch (error) {
            this.logger.error(error, `Failed to publish message to subject ${subject}:`);
        }
    }

    getClient() {
        return this.natsClient;
    }
}
