{"name": "xbit-user-service", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "tsc -p tsconfig.migrations.json && nest build", "format": "prettier --write \"apps/**/*.ts\" \"libs/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:public-graphql": "MIKRO_ORM_CLI_USE_TS_NODE=false npx mikro-orm migration:up --config ./dist/apps/public-graphql/mikro-orm.config.js && node dist/apps/public-graphql/apps/public-graphql/src/main.js", "start:grpc-internal": "node dist/apps/internal-grpc/apps/internal-grpc/src/main.js", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./apps/public-graphql/test/jest-e2e.json", "prepare": "husky"}, "dependencies": {"@apollo/server": "^4.11.3", "@clickhouse/client": "^1.10.1", "@grpc/grpc-js": "^1.13.3", "@grpc/proto-loader": "^0.7.13", "@grpc/reflection": "^1.0.4", "@keyv/redis": "^4.2.0", "@mikro-orm/cli": "^6.4.5", "@mikro-orm/core": "^6.4.5", "@mikro-orm/entity-generator": "^6.4.5", "@mikro-orm/migrations": "^6.4.5", "@mikro-orm/nestjs": "^6.1.0", "@mikro-orm/postgresql": "^6.4.5", "@mikro-orm/seeder": "^6.4.5", "@mikro-orm/sql-highlighter": "^1.0.1", "@msgpack/msgpack": "^3.1.1", "@nestjs/apollo": "^13.0.2", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.1", "@nestjs/core": "^11.0.1", "@nestjs/graphql": "^13.0.2", "@nestjs/jwt": "^11.0.0", "@nestjs/microservices": "^11.0.16", "@nestjs/mongoose": "^11.0.1", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@nestjs/schedule": "^5.0.1", "@nestjs/terminus": "^11.0.0", "@sentry/minimal": "^6.19.7", "@sentry/nestjs": "^9.36.0", "@sentry/node": "^9.35.0", "@sentry/profiling-node": "^9.36.0", "@solana/spl-token": "^0.4.13", "@solana/web3.js": "^1.98.0", "@turnkey/http": "^3.4.0", "@turnkey/sdk-server": "^4.0.0", "@turnkey/wallet-stamper": "^1.0.4", "@types/async-retry": "^1.4.9", "async-mutex": "^0.5.0", "axios": "^1.8.1", "cache-manager": "^6.4.0", "class-validator": "^0.14.1", "dayjs": "^1.11.13", "decimal.js": "^10.5.0", "dotenv": "^16.4.7", "ethers": "^6.13.5", "google-auth-library": "^9.15.1", "graphql": "^16.10.0", "graphql-scalars": "^1.24.2", "i18n": "^0.15.1", "ioredis": "^5.6.0", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.2.0", "lru-cache": "^11.1.0", "mongoose": "^8.10.0", "mqtt": "^5.10.4", "nats": "^2.29.3", "nest-commander": "^3.17.0", "nestjs-i18n": "^10.5.1", "nestjs-pino": "4.3.1", "nestjs-telegraf": "^2.8.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pino": "^8.17.2", "pino-http": "^9.0.0", "pino-pretty": "^10.2.3", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "snappy": "^7.2.2", "speakeasy": "^2.0.0", "telegraf": "^4.16.3", "tronweb": "^6.0.1", "tweetnacl": "^1.0.3", "tweetnacl-util": "^0.15.1", "uuidv7": "^1.0.2"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^5.0.0", "@types/i18n": "^0.13.12", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^15.14.0", "husky": "^9.1.7", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "dependencies": {"@apollo/server": "^4.11.3", "@clickhouse/client": "^1.10.1", "@grpc/grpc-js": "^1.13.3", "@grpc/proto-loader": "^0.7.13", "@grpc/reflection": "^1.0.4", "@keyv/redis": "^4.2.0", "@mikro-orm/cli": "^6.4.5", "@mikro-orm/core": "^6.4.5", "@mikro-orm/entity-generator": "^6.4.5", "@mikro-orm/migrations": "^6.4.5", "@mikro-orm/nestjs": "^6.1.0", "@mikro-orm/postgresql": "^6.4.5", "@mikro-orm/seeder": "^6.4.5", "@mikro-orm/sql-highlighter": "^1.0.1", "@msgpack/msgpack": "^3.1.1", "@nestjs/apollo": "^13.0.2", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.1", "@nestjs/core": "^11.0.1", "@nestjs/graphql": "^13.0.2", "@nestjs/jwt": "^11.0.0", "@nestjs/microservices": "^11.0.16", "@nestjs/mongoose": "^11.0.1", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@nestjs/schedule": "^5.0.1", "@solana/spl-token": "^0.4.13", "@solana/web3.js": "^1.98.0", "@turnkey/http": "^3.4.0", "@turnkey/sdk-server": "^4.0.0", "@turnkey/wallet-stamper": "^1.0.4", "@types/async-retry": "^1.4.9", "async-mutex": "^0.5.0", "axios": "^1.8.1", "cache-manager": "^6.4.0", "class-validator": "^0.14.1", "dayjs": "^1.11.13", "decimal.js": "^10.5.0", "dotenv": "^16.4.7", "ethers": "^6.13.5", "google-auth-library": "^9.15.1", "graphql": "^16.10.0", "graphql-scalars": "^1.24.2", "ioredis": "^5.6.0", "lru-cache": "^11.1.0", "mongoose": "^8.10.0", "mqtt": "^5.10.4", "nats": "^2.29.3", "nestjs-pino": "4.3.1", "nestjs-telegraf": "^2.8.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pino": "^8.17.2", "pino-http": "^9.0.0", "pino-pretty": "^10.2.3", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "snappy": "^7.2.2", "speakeasy": "^2.0.0", "telegraf": "^4.16.3", "tronweb": "^6.0.1", "tweetnacl": "^1.0.3", "tweetnacl-util": "^0.15.1", "uuidv7": "^1.0.2"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/speakeasy": "^2.0.10", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^15.14.0", "husky": "^9.1.7", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "./coverage", "testEnvironment": "node", "roots": ["<rootDir>/apps/", "<rootDir>/libs/"], "moduleNameMapper": {"^@lib/internal/turnkey(|/.*)$": "<rootDir>/libs/internal/turnkey/src/$1", "^@lib/mqtt(|/.*)$": "<rootDir>/libs/mqtt/src/$1", "^@lib/redis(|/.*)$": "<rootDir>/libs/redis/src/$1", "^@libs/internal/vault-management(|/.*)$": "<rootDir>/libs/internal/vault-management/src/$1", "^@libs/logger(|/.*)$": "<rootDir>/libs/logger/src/$1", "^@libs/sentry(|/.*)$": "<rootDir>/libs/sentry/src/$1", "^@libs/test(|/.*)$": "<rootDir>/libs/test/src/$1", "^libs/internal/rate-limiting(|/.*)$": "<rootDir>/libs/internal/rate-limiting/src/$1", "^libs/redis(|/.*)$": "<rootDir>/libs/redis/src/$1"}}, "roots": ["<rootDir>/apps/", "<rootDir>/libs/"], "moduleNameMapper": {"^libs/sentry(|/.*)$": "<rootDir>/libs/sentry/src/$1"}}}